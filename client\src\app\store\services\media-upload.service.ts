import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { CMS_APIContstant } from '../../constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class MediaUploadService {
  private folderCache = new Map<string, number>();
  private readonly targetFolder = 'CRM/Attachments';

  constructor(private http: HttpClient) { }

  /**
   * Upload file to media library with specified path
   * @param file - File to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload response
   */
  uploadFile(file: File, path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('path', path);

    // Set headers for file upload
    const headers = new HttpHeaders();
    // Don't set Content-Type header, let browser set it with boundary for multipart/form-data

    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });
  }

  /**
   * Enhanced upload file with folder creation and CRM attachment record
   * @param file - File to upload
   * @param activityId - Activity ID for CRM attachment record
   * @param createFolderStructure - Whether to create folder structure (default: true)
   * @returns Observable with upload result and CRM attachment record
   */
  uploadFileWithCRMAttachment(
    file: File,
    activityId?: string,
    createFolderStructure: boolean = true
  ): Observable<any> {
    // Validate file first
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      return throwError(() => new Error(validation.error));
    }

    console.log(`Starting upload: ${file.name} (${this.formatFileSize(file.size)})`);

    // Step 1: Ensure folder exists if required
    const folderObservable = createFolderStructure
      ? this.ensureFolderExists(this.targetFolder)
      : of(null);

    return folderObservable.pipe(
      switchMap((folderId: number | null) => {
        console.log(`Target folder ID: ${folderId || 'root'}`);

        // Step 2: Upload file to Strapi
        return this.uploadToStrapi(file, folderId);
      }),
      switchMap((uploadResult: any) => {
        console.log(`✅ Upload successful!`);
        console.log(`   File: ${file.name}`);
        console.log(`   Strapi ID: ${uploadResult.id}`);
        console.log(`   URL: ${uploadResult.url}`);
        console.log(`   Folder: ${this.targetFolder}`);

        // Step 3: Create CRM attachment record if activityId provided
        if (activityId) {
          return this.createCRMAttachmentRecord(uploadResult, activityId).pipe(
            map((crmRecord: any) => ({
              success: true,
              file: uploadResult,
              crmAttachment: crmRecord,
              message: 'File uploaded and CRM attachment record created successfully'
            }))
          );
        } else {
          return of({
            success: true,
            file: uploadResult,
            message: 'File uploaded successfully'
          });
        }
      }),
      catchError((error: any) => {
        console.error(`❌ Upload failed: ${error.message}`);
        return throwError(() => ({
          success: false,
          error: error.message,
          message: 'File upload failed'
        }));
      })
    );
  }

  /**
   * Upload multiple files to media library
   * @param files - Array of files to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload response
   */
  uploadMultipleFiles(files: File[], path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    formData.append('path', path);

    const headers = new HttpHeaders();
    
    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });
  }

  /**
   * Upload file with progress tracking
   * @param file - File to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload progress and response
   */
  uploadFileWithProgress(file: File, path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('path', path);

    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {
      reportProgress: true,
      observe: 'events'
    });
  }

  /**
   * Delete uploaded file
   * @param fileId - ID of the file to delete
   * @returns Observable with delete response
   */
  deleteFile(fileId: string): Observable<any> {
    return this.http.delete(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);
  }

  /**
   * Get file details by ID
   * @param fileId - ID of the file
   * @returns Observable with file details
   */
  getFileDetails(fileId: string): Observable<any> {
    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);
  }

  /**
   * Get files by path
   * @param path - Path to search files (e.g., 'CRM/Attachments')
   * @returns Observable with files list
   */
  getFilesByPath(path: string): Observable<any> {
    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}?path=${encodeURIComponent(path)}`);
  }

  /**
   * Validate file before upload
   * @param file - File to validate
   * @param maxSize - Maximum file size in bytes (default: 10MB)
   * @param allowedTypes - Array of allowed MIME types
   * @returns Validation result
   */
  validateFile(
    file: File, 
    maxSize: number = 10 * 1024 * 1024, // 10MB default
    allowedTypes?: string[]
  ): { isValid: boolean; error?: string } {
    
    // Check file size
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size exceeds maximum limit of ${this.formatFileSize(maxSize)}`
      };
    }

    // Check file type if specified
    if (allowedTypes && allowedTypes.length > 0) {
      if (!allowedTypes.includes(file.type)) {
        return {
          isValid: false,
          error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Format file size for display
   * @param bytes - File size in bytes
   * @returns Formatted file size string
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Generate unique filename to avoid conflicts
   * @param originalName - Original filename
   * @returns Unique filename with timestamp
   */
  generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const extension = originalName.substring(originalName.lastIndexOf('.'));
    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
    return `${nameWithoutExt}_${timestamp}${extension}`;
  }

  /**
   * Upload file to Strapi with folder support
   * @param file - File to upload
   * @param folderId - Optional folder ID
   * @returns Observable with upload result
   */
  private uploadToStrapi(file: File, folderId: number | null = null): Observable<any> {
    const formData = new FormData();
    formData.append('file', file, file.name);

    // Add folder ID if available
    if (folderId) {
      formData.append('folderId', folderId.toString());
    }

    const headers = new HttpHeaders();
    // Don't set Content-Type header for multipart/form-data

    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers }).pipe(
      map((response: any) => {
        // Handle plugin response format
        let uploadedFile;
        if (response && response.data && Array.isArray(response.data)) {
          uploadedFile = response.data[0];
        } else if (response && Array.isArray(response)) {
          uploadedFile = response[0];
        } else if (response) {
          uploadedFile = response;
        } else {
          throw new Error('Invalid response from Strapi');
        }

        if (!uploadedFile) {
          throw new Error('No file data in response');
        }

        return uploadedFile;
      }),
      catchError((error: any) => {
        console.error('Upload to Strapi failed:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Ensure folder structure exists
   * @param folderPath - Path to create (e.g., 'CRM/Attachments')
   * @returns Observable with folder ID
   */
  private ensureFolderExists(folderPath: string): Observable<number | null> {
    // Check cache first
    if (this.folderCache.has(folderPath)) {
      return of(this.folderCache.get(folderPath) || null);
    }

    // Split path into parts
    const pathParts = folderPath.split('/').filter(part => part && part !== '.');
    let currentPath = '';

    // Create folders sequentially
    return this.createFoldersSequentially(pathParts, null, currentPath, folderPath);
  }

  /**
   * Create folders sequentially in hierarchy
   */
  private createFoldersSequentially(
    pathParts: string[],
    currentFolderId: number | null,
    currentPath: string,
    fullPath: string
  ): Observable<number | null> {
    if (pathParts.length === 0) {
      // Cache the final folder ID
      if (currentFolderId) {
        this.folderCache.set(fullPath, currentFolderId);
      }
      return of(currentFolderId);
    }

    const folderName = pathParts[0];
    const remainingParts = pathParts.slice(1);
    currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;

    // Check if this partial path is cached
    if (this.folderCache.has(currentPath)) {
      const cachedId = this.folderCache.get(currentPath) || null;
      return this.createFoldersSequentially(remainingParts, cachedId, currentPath, fullPath);
    }

    // Create or get folder
    return this.createOrGetFolder(folderName, currentFolderId).pipe(
      switchMap((folderId: number | null) => {
        if (folderId === null) {
          console.warn(`Failed to create folder: ${folderName}`);
          return of(null);
        }

        // Cache the folder ID
        this.folderCache.set(currentPath, folderId);

        // Continue with remaining parts
        return this.createFoldersSequentially(remainingParts, folderId, currentPath, fullPath);
      })
    );
  }

  /**
   * Create or get existing folder
   * @param folderName - Name of the folder
   * @param parentId - Parent folder ID
   * @returns Observable with folder ID
   */
  private createOrGetFolder(folderName: string, parentId: number | null = null): Observable<number | null> {
    // First, try to find if folder already exists
    return this.findFolder(folderName, parentId).pipe(
      switchMap((existingFolder: any) => {
        if (existingFolder) {
          console.log(`Found existing folder: ${folderName} (ID: ${existingFolder.id})`);
          return of(existingFolder.id);
        }

        // Create new folder
        const requestBody: any = { name: folderName };
        if (parentId) {
          requestBody.parentId = parentId;
        }

        const headers = new HttpHeaders({
          'Content-Type': 'application/json'
        });

        return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}`, requestBody, { headers }).pipe(
          map((response: any) => {
            if (!response || !response.id) {
              throw new Error('Invalid response when creating folder');
            }
            console.log(`Created folder: ${folderName} (ID: ${response.id})`);
            return response.id;
          }),
          catchError((error: any) => {
            // Handle folder already exists error
            if (error.status === 400 || error.status === 500) {
              return this.findFolder(folderName, parentId).pipe(
                map((folder: any) => {
                  if (folder) {
                    console.log(`Found folder after conflict: ${folderName} (ID: ${folder.id})`);
                    return folder.id;
                  }
                  return null;
                })
              );
            }

            // If media library plugin not available, return null
            if (error.status === 404 || error.status === 403) {
              console.warn(`Media library plugin not available or permission denied`);
              return of(null);
            }

            console.error(`Error creating folder ${folderName}: ${error.message}`);
            return of(null);
          })
        );
      })
    );
  }

  /**
   * Find existing folder by name and parent
   * @param folderName - Name of the folder to find
   * @param parentId - Parent folder ID
   * @returns Observable with folder data or null
   */
  private findFolder(folderName: string, parentId: number | null = null): Observable<any> {
    const params = new URLSearchParams();
    if (parentId) {
      params.append('parentId', parentId.toString());
    }

    const url = `${CMS_APIContstant.MEDIA_UPLOAD.replace('/files', '/folders')}?${params.toString()}`;

    return this.http.get(url).pipe(
      map((response: any) => {
        if (response && Array.isArray(response)) {
          return response.find((folder: any) => folder.name === folderName);
        }
        return null;
      }),
      catchError((error: any) => {
        if (error.status === 404 || error.status === 403) {
          return of(null);
        }
        console.warn(`Error searching for folder ${folderName}: ${error.message}`);
        return of(null);
      })
    );
  }

  /**
   * Create CRM attachment record
   * @param uploadResult - Upload result from Strapi
   * @param activityId - Activity ID for the attachment
   * @returns Observable with CRM attachment record
   */
  private createCRMAttachmentRecord(uploadResult: any, activityId: string): Observable<any> {
    const attachmentData = {
      mime_type: uploadResult.mime || uploadResult.mimeType || 'application/octet-stream',
      name: uploadResult.name || uploadResult.filename,
      link_web_uri: uploadResult.url,
      activity_id: activityId
    };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, attachmentData, { headers }).pipe(
      map((response: any) => {
        console.log(`✅ CRM attachment record created: ${response.id}`);
        return response;
      }),
      catchError((error: any) => {
        console.error('Failed to create CRM attachment record:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Upload multiple files with CRM attachment records
   * @param files - Array of files to upload
   * @param activityId - Activity ID for CRM attachment records
   * @param createFolderStructure - Whether to create folder structure
   * @returns Observable with array of upload results
   */
  uploadMultipleFilesWithCRMAttachment(
    files: File[],
    activityId?: string,
    createFolderStructure: boolean = true
  ): Observable<any[]> {
    const uploadObservables = files.map(file =>
      this.uploadFileWithCRMAttachment(file, activityId, createFolderStructure)
    );

    // Use forkJoin to wait for all uploads to complete
    return new Observable(observer => {
      let completedUploads = 0;
      const results: any[] = [];

      uploadObservables.forEach((upload$, index) => {
        upload$.subscribe({
          next: (result) => {
            results[index] = result;
            completedUploads++;

            if (completedUploads === files.length) {
              observer.next(results);
              observer.complete();
            }
          },
          error: (error) => {
            results[index] = { success: false, error: error.message };
            completedUploads++;

            if (completedUploads === files.length) {
              observer.next(results);
              observer.complete();
            }
          }
        });
      });
    });
  }

  /**
   * Get CRM attachments by activity ID
   * @param activityId - Activity ID to filter attachments
   * @returns Observable with attachments list
   */
  getCRMAttachmentsByActivity(activityId: string): Observable<any> {
    return this.http.get(`${CMS_APIContstant.CRM_ATTACHMENTS}?activity_id=${activityId}`);
  }

  /**
   * Delete CRM attachment record
   * @param attachmentId - ID of the attachment to delete
   * @returns Observable with delete response
   */
  deleteCRMAttachment(attachmentId: string): Observable<any> {
    return this.http.delete(`${CMS_APIContstant.CRM_ATTACHMENTS}/${attachmentId}`);
  }

  /**
   * Clear folder cache (useful when folder structure changes)
   */
  clearFolderCache(): void {
    this.folderCache.clear();
    console.log('Folder cache cleared');
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CMS_APIContstant } from '../../constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class MediaUploadService {

  constructor(private http: HttpClient) { }

  /**
   * Upload file to media library with specified path
   * @param file - File to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload response
   */
  uploadFile(file: File, path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('path', path);

    // Set headers for file upload
    const headers = new HttpHeaders();
    // Don't set Content-Type header, let browser set it with boundary for multipart/form-data

    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });
  }

  /**
   * Upload multiple files to media library
   * @param files - Array of files to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload response
   */
  uploadMultipleFiles(files: File[], path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    formData.append('path', path);

    const headers = new HttpHeaders();
    
    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, { headers });
  }

  /**
   * Upload file with progress tracking
   * @param file - File to upload
   * @param path - Upload path (e.g., 'CRM/Attachments')
   * @returns Observable with upload progress and response
   */
  uploadFileWithProgress(file: File, path: string = 'CRM/Attachments'): Observable<any> {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('path', path);

    return this.http.post(`${CMS_APIContstant.MEDIA_UPLOAD}`, formData, {
      reportProgress: true,
      observe: 'events'
    });
  }

  /**
   * Delete uploaded file
   * @param fileId - ID of the file to delete
   * @returns Observable with delete response
   */
  deleteFile(fileId: string): Observable<any> {
    return this.http.delete(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);
  }

  /**
   * Get file details by ID
   * @param fileId - ID of the file
   * @returns Observable with file details
   */
  getFileDetails(fileId: string): Observable<any> {
    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}/${fileId}`);
  }

  /**
   * Get files by path
   * @param path - Path to search files (e.g., 'CRM/Attachments')
   * @returns Observable with files list
   */
  getFilesByPath(path: string): Observable<any> {
    return this.http.get(`${CMS_APIContstant.MEDIA_UPLOAD}?path=${encodeURIComponent(path)}`);
  }

  /**
   * Validate file before upload
   * @param file - File to validate
   * @param maxSize - Maximum file size in bytes (default: 10MB)
   * @param allowedTypes - Array of allowed MIME types
   * @returns Validation result
   */
  validateFile(
    file: File, 
    maxSize: number = 10 * 1024 * 1024, // 10MB default
    allowedTypes?: string[]
  ): { isValid: boolean; error?: string } {
    
    // Check file size
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size exceeds maximum limit of ${this.formatFileSize(maxSize)}`
      };
    }

    // Check file type if specified
    if (allowedTypes && allowedTypes.length > 0) {
      if (!allowedTypes.includes(file.type)) {
        return {
          isValid: false,
          error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Format file size for display
   * @param bytes - File size in bytes
   * @returns Formatted file size string
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Generate unique filename to avoid conflicts
   * @param originalName - Original filename
   * @returns Unique filename with timestamp
   */
  generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const extension = originalName.substring(originalName.lastIndexOf('.'));
    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
    return `${nameWithoutExt}_${timestamp}${extension}`;
  }
}

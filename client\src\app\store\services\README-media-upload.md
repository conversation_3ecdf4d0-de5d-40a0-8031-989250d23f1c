# Enhanced Media Upload Service

This service provides comprehensive file upload functionality with automatic folder creation and CRM attachment record management.

## Features

- ✅ Upload files to CRM/Attachments folder structure
- ✅ Automatic folder creation in Strapi media library
- ✅ CRM attachment record creation
- ✅ File validation (size, type)
- ✅ Multiple file upload support
- ✅ Progress tracking
- ✅ Folder caching for performance
- ✅ Error handling and recovery

## Basic Usage

### Single File Upload with CRM Attachment

```typescript
import { MediaUploadService } from './store/services/media-upload.service';

constructor(private mediaUploadService: MediaUploadService) {}

uploadFile(file: File, activityId: string) {
  this.mediaUploadService.uploadFileWithCRMAttachment(file, activityId)
    .subscribe({
      next: (result) => {
        if (result.success) {
          console.log('File uploaded:', result.file);
          console.log('CRM attachment created:', result.crmAttachment);
        }
      },
      error: (error) => {
        console.error('Upload failed:', error);
      }
    });
}
```

### Multiple Files Upload

```typescript
uploadMultipleFiles(files: File[], activityId: string) {
  this.mediaUploadService.uploadMultipleFilesWithCRMAttachment(files, activityId)
    .subscribe({
      next: (results) => {
        results.forEach((result, index) => {
          if (result.success) {
            console.log(`File ${index + 1} uploaded successfully`);
          } else {
            console.error(`File ${index + 1} failed:`, result.error);
          }
        });
      }
    });
}
```

### File Upload without CRM Attachment

```typescript
// Upload to CRM/Attachments folder without creating CRM record
uploadFileOnly(file: File) {
  this.mediaUploadService.uploadFileWithCRMAttachment(file)
    .subscribe({
      next: (result) => {
        console.log('File uploaded:', result.file);
      }
    });
}
```

## API Methods

### Core Upload Methods

#### `uploadFileWithCRMAttachment(file, activityId?, createFolderStructure?)`
Enhanced upload with folder creation and optional CRM attachment record.

**Parameters:**
- `file: File` - File to upload
- `activityId?: string` - Optional activity ID for CRM attachment
- `createFolderStructure?: boolean` - Whether to create folder structure (default: true)

**Returns:** `Observable<UploadResult>`

#### `uploadMultipleFilesWithCRMAttachment(files, activityId?, createFolderStructure?)`
Upload multiple files with CRM attachment records.

**Parameters:**
- `files: File[]` - Array of files to upload
- `activityId?: string` - Optional activity ID for CRM attachments
- `createFolderStructure?: boolean` - Whether to create folder structure (default: true)

**Returns:** `Observable<UploadResult[]>`

### Legacy Methods (Still Available)

#### `uploadFile(file, path?)`
Basic file upload to specified path.

#### `uploadMultipleFiles(files, path?)`
Upload multiple files to specified path.

#### `uploadFileWithProgress(file, path?)`
Upload with progress tracking.

### CRM Attachment Management

#### `getCRMAttachmentsByActivity(activityId)`
Get all CRM attachments for a specific activity.

#### `deleteCRMAttachment(attachmentId)`
Delete a CRM attachment record.

### Utility Methods

#### `validateFile(file, maxSize?, allowedTypes?)`
Validate file before upload.

#### `formatFileSize(bytes)`
Format file size for display.

#### `generateUniqueFilename(originalName)`
Generate unique filename with timestamp.

#### `clearFolderCache()`
Clear the folder cache.

## Configuration

The service automatically:
- Creates `CRM/Attachments` folder structure
- Validates file size and type
- Handles folder creation conflicts
- Caches folder IDs for performance

## File Structure Created

```
Media Library/
└── CRM/
    └── Attachments/
        ├── uploaded-file-1.pdf
        ├── uploaded-file-2.jpg
        └── ...
```

## CRM Attachment Record

When `activityId` is provided, the service creates a record in `/api/crm-attachments` with:

```json
{
  "mime_type": "application/pdf",
  "name": "document.pdf",
  "link_web_uri": "https://cms.example.com/uploads/document.pdf",
  "activity_id": "activity-123"
}
```

## Error Handling

The service handles various error scenarios:
- File validation errors
- Network timeouts
- Folder creation conflicts
- CRM attachment creation failures
- Media library plugin unavailability

## Example Component

See `examples/media-upload-example.component.ts` for a complete implementation example.

## File Validation

Default validation rules:
- Maximum file size: 10MB
- Supported extensions: Based on MIME type
- File existence check

Custom validation:
```typescript
const validation = this.mediaUploadService.validateFile(
  file, 
  5 * 1024 * 1024, // 5MB max
  ['image/jpeg', 'image/png', 'application/pdf'] // Allowed types
);

if (!validation.isValid) {
  console.error(validation.error);
}
```

## Performance Considerations

- Folder IDs are cached to avoid repeated API calls
- Use `clearFolderCache()` if folder structure changes externally
- Multiple file uploads are processed concurrently
- Progress tracking available for large files

## Dependencies

- Angular HttpClient
- RxJS Observables
- Strapi Media Library Plugin
- CRM Attachments API endpoint

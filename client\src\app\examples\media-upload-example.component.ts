import { Component } from '@angular/core';
import { MediaUploadService } from '../store/services/media-upload.service';

@Component({
  selector: 'app-media-upload-example',
  template: `
    <div class="upload-container">
      <h3>File Upload with CRM Attachment</h3>
      
      <!-- Single File Upload -->
      <div class="upload-section">
        <h4>Single File Upload</h4>
        <input 
          type="file" 
          (change)="onSingleFileSelected($event)"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
        />
        <input 
          type="text" 
          [(ngModel)]="activityId" 
          placeholder="Activity ID (optional)"
        />
        <button 
          (click)="uploadSingleFile()" 
          [disabled]="!selectedFile || uploading"
        >
          {{ uploading ? 'Uploading...' : 'Upload File' }}
        </button>
      </div>

      <!-- Multiple Files Upload -->
      <div class="upload-section">
        <h4>Multiple Files Upload</h4>
        <input 
          type="file" 
          (change)="onMultipleFilesSelected($event)"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
          multiple
        />
        <button 
          (click)="uploadMultipleFiles()" 
          [disabled]="!selectedFiles?.length || uploading"
        >
          {{ uploading ? 'Uploading...' : 'Upload Files' }}
        </button>
      </div>

      <!-- Upload Results -->
      <div class="results-section" *ngIf="uploadResults.length > 0">
        <h4>Upload Results</h4>
        <div *ngFor="let result of uploadResults" class="result-item">
          <div [ngClass]="{'success': result.success, 'error': !result.success}">
            <strong>{{ result.file?.name || 'Unknown file' }}</strong>
            <p>{{ result.message }}</p>
            <div *ngIf="result.success">
              <p>File URL: <a [href]="result.file.url" target="_blank">{{ result.file.url }}</a></p>
              <p *ngIf="result.crmAttachment">CRM Attachment ID: {{ result.crmAttachment.id }}</p>
            </div>
            <div *ngIf="!result.success">
              <p class="error-message">Error: {{ result.error }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CRM Attachments List -->
      <div class="attachments-section" *ngIf="activityId">
        <h4>CRM Attachments for Activity: {{ activityId }}</h4>
        <button (click)="loadCRMAttachments()">Load Attachments</button>
        <div *ngFor="let attachment of crmAttachments" class="attachment-item">
          <p><strong>{{ attachment.name }}</strong></p>
          <p>Type: {{ attachment.mime_type }}</p>
          <p>URL: <a [href]="attachment.link_web_uri" target="_blank">View File</a></p>
          <button (click)="deleteCRMAttachment(attachment.id)">Delete</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .upload-container {
      padding: 20px;
      max-width: 800px;
    }
    
    .upload-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .result-item {
      margin: 10px 0;
      padding: 10px;
      border-radius: 3px;
    }
    
    .success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
    }
    
    .error {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
    }
    
    .error-message {
      color: #721c24;
    }
    
    .attachment-item {
      margin: 10px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 3px;
    }
    
    input, button {
      margin: 5px;
      padding: 8px;
    }
    
    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  `]
})
export class MediaUploadExampleComponent {
  selectedFile: File | null = null;
  selectedFiles: FileList | null = null;
  activityId: string = '';
  uploading: boolean = false;
  uploadResults: any[] = [];
  crmAttachments: any[] = [];

  constructor(private mediaUploadService: MediaUploadService) {}

  onSingleFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file before setting
      const validation = this.mediaUploadService.validateFile(file);
      if (validation.isValid) {
        this.selectedFile = file;
      } else {
        alert(validation.error);
        event.target.value = '';
      }
    }
  }

  onMultipleFilesSelected(event: any): void {
    this.selectedFiles = event.target.files;
  }

  uploadSingleFile(): void {
    if (!this.selectedFile) return;

    this.uploading = true;
    this.uploadResults = [];

    this.mediaUploadService.uploadFileWithCRMAttachment(
      this.selectedFile, 
      this.activityId || undefined
    ).subscribe({
      next: (result) => {
        this.uploadResults = [result];
        this.uploading = false;
        console.log('Upload result:', result);
      },
      error: (error) => {
        this.uploadResults = [error];
        this.uploading = false;
        console.error('Upload error:', error);
      }
    });
  }

  uploadMultipleFiles(): void {
    if (!this.selectedFiles?.length) return;

    this.uploading = true;
    this.uploadResults = [];

    const filesArray = Array.from(this.selectedFiles);

    this.mediaUploadService.uploadMultipleFilesWithCRMAttachment(
      filesArray, 
      this.activityId || undefined
    ).subscribe({
      next: (results) => {
        this.uploadResults = results;
        this.uploading = false;
        console.log('Upload results:', results);
      },
      error: (error) => {
        this.uploadResults = [{ success: false, error: error.message }];
        this.uploading = false;
        console.error('Upload error:', error);
      }
    });
  }

  loadCRMAttachments(): void {
    if (!this.activityId) return;

    this.mediaUploadService.getCRMAttachmentsByActivity(this.activityId).subscribe({
      next: (attachments) => {
        this.crmAttachments = attachments.data || attachments;
        console.log('CRM Attachments:', this.crmAttachments);
      },
      error: (error) => {
        console.error('Error loading CRM attachments:', error);
        alert('Failed to load CRM attachments');
      }
    });
  }

  deleteCRMAttachment(attachmentId: string): void {
    if (confirm('Are you sure you want to delete this attachment?')) {
      this.mediaUploadService.deleteCRMAttachment(attachmentId).subscribe({
        next: () => {
          this.crmAttachments = this.crmAttachments.filter(a => a.id !== attachmentId);
          console.log('Attachment deleted successfully');
        },
        error: (error) => {
          console.error('Error deleting attachment:', error);
          alert('Failed to delete attachment');
        }
      });
    }
  }
}

import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CMS_APIContstant } from 'src/app/constants/api.constants';
import { AuthService } from 'src/app/core/authentication/auth.service';
import { MediaUploadService } from '../services/media-upload.service';

@Injectable({
  providedIn: 'root',
})
export class ActivitiesService {
  public activitySubject = new BehaviorSubject<any>(null);
  public activity = this.activitySubject.asObservable();

  constructor(
    private http: HttpClient,
    private authservice: AuthService,
    private mediaUploadService: MediaUploadService
  ) {}

  createNote(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {
      data,
    });
  }

  createActivity(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_ACTIVITY_PHONE_CALL_REGISTRATION}`,
      data
    );
  }

  createActivityTask(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_ACTIVITY_TASK_REGISTRATION}`,
      data
    );
  }

  createFollowup(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOW_UP_REGISTRATION}`,
      data
    );
  }

  createOpportuniyFollowup(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_OPPORTUNITY_ACTIVITY_FOLLOW_UP_REGISTRATION}`,
      data
    );
  }

  createInvolvedParty(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_INVOLVED_PARTIES}`, {
      data,
    });
  }

  createRelatedItem(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}`,
      {
        data,
      }
    );
  }

  updateActivity(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, {
      data,
    });
  }

  updateNote(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {
      data,
    });
  }

  updateActivityStatus(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_ACTIVITY}/${Id}`, { data });
  }

  deleteNote(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);
  }

  deleteInvolvedParty(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_INVOLVED_PARTIES}/${id}`
    );
  }

  deleteFollowupItem(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_ACTIVITY_FOLLOWUP_RELATED_ITEMS}/${id}`
    );
  }

  getActivityDropdownOptions(type: string) {
    const params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', type);

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getActivities(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString())
      .set(
        'fields',
        'main_account_party_id,subject,activity_status,start_date,end_date,category'
      );

    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    }

    if (searchTerm) {
      params = params.set(
        'filters[$or][0][main_account_party_id][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);
      params = params.set('filters[$or][2][category][$containsi]', searchTerm);
    }

    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });
  }

  getSalesCall(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string,
    filter?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('filters[document_type][$eq]', '0002')
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString())
      .set(
        'fields',
        'main_account_party_id,activity_id,subject,owner_party_id,brand,customer_group,ranking,activity_status,phone_call_category,customer_timezone,createdAt,updatedAt'
      )
      .set('populate[notes][fields][0]', 'note')
      .set('populate[notes][fields][1]', 'is_global_note')
      .set(
        'populate[business_partner][populate][addresses][fields][0]',
        'region'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][1]',
        'country'
      )
      .set('populate[business_partner][fields][0]', 'bp_full_name')
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');

    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    } else {
      // Default sort by updatedAt descending
      params = params.set('sort', 'updatedAt:desc');
    }

    if (searchTerm) {
      params = params.set(
        'filters[$or][0][main_account_party_id][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);
      params = params.set(
        'filters[$or][2][phone_call_category][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][3][brand][$containsi]', searchTerm);
      params = params.set(
        'filters[$or][4][business_partner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][5][ranking][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][6][business_partner_owner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][7][customer_group][$containsi]',
        searchTerm
      );
      
    }
    if (filter) {
      if (filter === 'MSCT') {
        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();
        const endOfDay = new Date(
          today.setHours(23, 59, 59, 999)
        ).toISOString();

        params = params
          .set('filters[createdAt][$gte]', startOfDay)
          .set('filters[createdAt][$lte]', endOfDay);
      } else if (filter === 'MSCTW') {
        const now = new Date();

        // Get the start of the week (Monday)
        const startOfWeek = new Date(now);
        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
        const diffToMonday = day === 0 ? -6 : 1 - day;
        startOfWeek.setDate(now.getDate() + diffToMonday);
        startOfWeek.setHours(0, 0, 0, 0);

        // Get the end of the week (Sunday)
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);

        const startISO = startOfWeek.toISOString();
        const endISO = endOfWeek.toISOString();

        params = params
          .set('filters[createdAt][$gte]', startISO)
          .set('filters[createdAt][$lte]', endISO);
      } else if (filter === 'MSCTM') {
        const now = new Date();

        // Start of the month: 1st day at 00:00:00
        const startOfMonth = new Date(
          now.getFullYear(),
          now.getMonth(),
          1,
          0,
          0,
          0,
          0
        );

        // End of the month: last day at 23:59:59.999
        const endOfMonth = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999
        );

        const startISO = startOfMonth.toISOString();
        const endISO = endOfMonth.toISOString();

        params = params
          .set('filters[createdAt][$gte]', startISO)
          .set('filters[createdAt][$lte]', endISO);
      } else if (filter === 'MCSC') {
        params = params.set('filters[activity_status][$eq]', '3');
      } else if (filter === 'MSC') {
        const email = this.authservice.getUserEmail();
        if (email) {
          params = params
            .set(
              `filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`,
              'YI'
            )
            .set(
              `filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,
              email
            );
        }
      }
    }

    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });
  }

  getTasks(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string,
    filter?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('filters[document_type][$eq]', '0006')
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString())
      .set(
        'fields',
        'main_account_party_id,activity_id,subject,owner_party_id,activity_status,priority,processor_party_id,task_category,start_date,end_date'
      )
      .set('populate[notes][fields][0]', 'note')
      .set('populate[notes][fields][1]', 'is_global_note')
      .set(
        'populate[business_partner][populate][addresses][fields][0]',
        'region'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][1]',
        'country'
      )
      .set('populate[business_partner][fields][0]', 'bp_full_name')
      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')
      .set('populate[business_partner_processor][fields][0]', 'bp_full_name')
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');

    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    } else {
      // Default sort by updatedAt descending
      params = params.set('sort', 'updatedAt:desc');
    }

    if (searchTerm) {
      params = params.set(
        'filters[$or][0][main_account_party_id][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][1][subject][$containsi]', searchTerm);
      params = params.set(
        'filters[$or][2][task_category][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][3][business_partner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][4][business_partner_contact][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][5][business_partner_owner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][6][business_partner_processor][bp_full_name][$containsi]',
        searchTerm
      );
    }
    if (filter) {
      if (filter === 'MTT') {
        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();
        const endOfDay = new Date(
          today.setHours(23, 59, 59, 999)
        ).toISOString();

        params = params
          .set('filters[createdAt][$gte]', startOfDay)
          .set('filters[createdAt][$lte]', endOfDay);
      } else if (filter === 'MTTW') {
        const now = new Date();

        // Get the start of the week (Monday)
        const startOfWeek = new Date(now);
        const day = now.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
        const diffToMonday = day === 0 ? -6 : 1 - day;
        startOfWeek.setDate(now.getDate() + diffToMonday);
        startOfWeek.setHours(0, 0, 0, 0);

        // Get the end of the week (Sunday)
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);

        const startISO = startOfWeek.toISOString();
        const endISO = endOfWeek.toISOString();

        params = params
          .set('filters[createdAt][$gte]', startISO)
          .set('filters[createdAt][$lte]', endISO);
      } else if (filter === 'MTTM') {
        const now = new Date();

        // Start of the month: 1st day at 00:00:00
        const startOfMonth = new Date(
          now.getFullYear(),
          now.getMonth(),
          1,
          0,
          0,
          0,
          0
        );

        // End of the month: last day at 23:59:59.999
        const endOfMonth = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          0,
          23,
          59,
          59,
          999
        );

        const startISO = startOfMonth.toISOString();
        const endISO = endOfMonth.toISOString();

        params = params
          .set('filters[createdAt][$gte]', startISO)
          .set('filters[createdAt][$lte]', endISO);
      } else if (filter === 'MCT') {
        params = params.set('filters[activity_status][$eq]', '3');
      } else if (filter === 'MOT') {
        params = params.set('filters[activity_status][$eq]', '1');
      } else if (filter === 'MTST') {
        // params = params.set('filters[activity_status][$eq]', '1');
      } else if (filter === 'MT') {
        const email = this.authservice.getUserEmail();
        if (email) {
          params = params
            .set(
              `filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`,
              'YI'
            )
            .set(
              `filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,
              email
            );
        }
      }
    }

    return this.http.get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });
  }

  getActivityByID(activityId: string) {
    const params = new HttpParams()
      .set('filters[activity_id][$eq]', activityId)
      .set('populate[business_partner][fields][0]', 'bp_full_name')
      .set('populate[business_partner][fields][1]', 'bp_id')
      .set(
        'populate[business_partner][populate][customer][populate][partner_functions][fields][0]',
        'partner_function'
      )
      .set(
        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]',
        'first_name'
      )
      .set(
        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]',
        'last_name'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][address_usages][fields][0]',
        'address_usage'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][0]',
        'house_number'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][1]',
        'street_name'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][2]',
        'city_name'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][3]',
        'region'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][4]',
        'country'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][5]',
        'postal_code'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][emails][fields][0]',
        'email_address'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',
        'phone_number'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]',
        'website_url'
      )
      .set(
        'populate[involved_parties][populate][business_partner][fields][0]',
        'bp_full_name'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][populate][address_usages][fields][0]',
        'address_usage'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][0]',
        'city_name'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][1]',
        'country'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][2]',
        'house_number'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][3]',
        'region'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][4]',
        'street_name'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][fields][5]',
        'postal_code'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]',
        'email_address'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][0]',
        'phone_number'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][addresses][populate][phone_numbers][fields][1]',
        'phone_number_type'
      )
      .set(
        'populate[involved_parties][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',
        'partner_function'
      )
      .set(
        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][fields][0]',
        'partner_function'
      )
      .set(
        'populate[follow_up_and_related_items][populate][activity_transaction][populate][business_partner][populate][customer][populate][partner_functions][populate][business_partner][fields][0]',
        'bp_full_name'
      )
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')
      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')
      .set('populate[business_partner_processor][fields][0]', 'bp_full_name')
      .set(
        'populate[business_partner_contact][populate][contact_persons][fields][0]',
        'bp_company_id'
      )
      .set('populate[notes][populate]', '*')
      .set('populate[opportunity_followups][populate]', '*');

    return this.http
      .get<any[]>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params })
      .pipe(
        map((response: any) => {
          const activityDetails = response?.data[0] || null;
          this.activitySubject.next(activityDetails);
          return response;
        })
      );
  }

  getPartners(params: any) {
    return this.http
      .get<any>(
        `${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]=*`,
        { params }
      )
      .pipe(
        map((response) =>
          (response?.data || []).map((item: any) => {
            const contact = item?.addresses?.[0];

            const email = contact?.emails?.[0]?.email_address || '';
            const mobile = (contact?.phone_numbers || [])
              .filter((item: any) => item.phone_number_type === '3')
              .map((item: any) => item.phone_number);

            return {
              bp_id: item?.bp_id || '',
              bp_full_name: item?.bp_full_name || '',
              email: email,
              mobile: mobile,
            };
          })
        )
      );
  }

  getPartnersContact(params: any) {
    return this.http
      .get<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, { params })
      .pipe(
        map((response) => {
          return (response?.data || []).map((item: any) => {
            const contact = item?.business_partner_person?.addresses?.[0];

            const email = contact?.emails?.[0]?.email_address || '';
            const mobile = (contact?.phone_numbers || [])
              .filter((ph: any) => ph.phone_number_type === '3')
              .map((ph: any) => ph.phone_number)
              .join(', '); // Combine multiple numbers into one string

            return {
              bp_id: item?.business_partner_person?.bp_id || '',
              bp_full_name: item?.business_partner_person?.bp_full_name || '',
              email: email,
              mobile: mobile,
            };
          });
        })
      );
  }

  getEmailwisePartner() {
    const email = this.authservice.getUserEmail() ?? '';

    let params = new HttpParams()
      .set(
        'filters[business_partner_person][addresses][emails][email_address][$containsi]',
        email
      )
      .set(
        'populate[business_partner_person][populate][addresses][populate]',
        '*'
      );

    return this.http
      .get<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}`, { params })
      .pipe(
        map((response) => {
          const firstItem = response?.data?.[0]?.business_partner_person;
          return firstItem?.bp_id || null;
        })
      );
  }

  getActivityCodeWise(params: any) {
    return this.http.get<any>(`${CMS_APIContstant.CRM_ACTIVITY}`, { params });
  }

  getCrmAttachments(activityId: string): Observable<any> {
    const params = new HttpParams()
      .set('filters[activity_id][$eq]', activityId)

    return this.http.get<any>(`${CMS_APIContstant.CRM_ATTACHMENTS}`, { params });
  }

  createCrmAttachment(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_ATTACHMENTS}`, { data });
  }

  uploadMediaFile(file: File): Observable<any> {
    return this.mediaUploadService.uploadFileWithCRMAttachment(file, 'CRM/Attachments');
  }

  getActivity(partnerId: string): Observable<{ data: any[]; meta: any }> {
    let params = new HttpParams()
      .set('filters[main_account_party_id][$eq]', partnerId)
      .set(
        'fields',
        'subject,activity_id,start_date,end_date,createdAt,activity_status,phone_call_category,priority,document_type'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',
        'phone_number'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][1]',
        'phone_number_type'
      )
      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')
      .set('populate[business_partner_organizer][fields][0]', 'bp_full_name')
      .set('populate[notes][fields][0]', 'note')
      .set('populate[notes][fields][1]', 'is_global_note');

    return this.http.get<{ data: any[]; meta: any }>(
      `${CMS_APIContstant.CRM_ACTIVITY}`,
      { params }
    );
  }

  getTodayRange() {
    const today = new Date();
    const start = new Date(today.setHours(0, 0, 0, 0)).toISOString();
    const end = new Date(today.setHours(23, 59, 59, 999)).toISOString();
    return { start, end };
  }

  getThisWeekRange() {
    const now = new Date();
    const startOfWeek = new Date(now);
    const day = now.getDay();
    const diffToMonday = day === 0 ? -6 : 1 - day;
    startOfWeek.setDate(now.getDate() + diffToMonday);
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return {
      start: startOfWeek.toISOString(),
      end: endOfWeek.toISOString(),
    };
  }

  getThisMonthRange() {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
    const end = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      0,
      23,
      59,
      59,
      999
    );
    return {
      start: start.toISOString(),
      end: end.toISOString(),
    };
  }
}
